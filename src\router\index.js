/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @LastEditTime: 2025-01-01
 * @Description: 杭韵e家活动页面路由配置
 */
import Vue from "vue";
import Router from "vue-router";

// 主要页面组件
const Home = () => import("@/pages/home/<USER>");
const Search = () => import("@/pages/search/search.vue");
const ActivityDetail = () => import("@/pages/activity/detail.vue");
const Profile = () => import("@/pages/profile/index.vue");
const Tabbar = () => import("@/pages/layout/tabbar.vue");
const Login = () => import("@/pages/Login/index.vue");
const Privacy = () => import("@/pages/privacy/index.vue");

Vue.use(Router);
export default new Router({
    routes: [
        {
            path: "/",
            name: "layout",
            component: Tabbar,
            redirect: "/home",
            children: [
                {
                    path: "home",
                    name: "home",
                    component: Home,
                    meta: {
                        title: "杭韵e家",
                        keepAlive: true,
                    },
                },
                {
                    path: "profile",
                    name: "profile",
                    component: Profile,
                    meta: {
                        title: "个人中心",
                        needLogin: true,
                    },
                },
            ],
        },
        // 搜索相关页面
        {
            path: "/search",
            name: "search",
            component: Search,
            meta: {
                title: "搜索活动",
            },
        },
        // 活动详情页
        {
            path: "/activity/:id",
            name: "activityDetail",
            component: ActivityDetail,
            meta: {
                title: "活动详情",
            },
        },
        // 登录页面
        {
            path: "/login",
            name: "login",
            component: Login,
            meta: {
                title: "登录",
            },
        },
        // 隐私协议
        {
            path: "/privacy",
            name: "privacy",
            component: Privacy,
            meta: {
                title: "隐私协议",
            },
        },
    ],
});
